@echo off
chcp 65001 >nul
echo.
echo 🚀 开始部署 cf-minesweeper 到 Cloudflare Workers...
echo Worker名称: test
echo KV命名空间: test
echo.

REM 检查是否已登录
echo 📋 检查 Wrangler 登录状态...
npx wrangler whoami >nul 2>&1
if errorlevel 1 (
    echo ❌ 未登录 Cloudflare，请先登录：
    echo    npx wrangler login
    pause
    exit /b 1
)

echo ✅ 已登录 Cloudflare
echo.

REM 创建KV命名空间
echo 🗄️ 创建KV命名空间...
echo 正在创建 KV 命名空间 'test'...

REM 创建生产环境KV
echo 创建生产环境 KV...
npx wrangler kv:namespace create "test"

echo.
echo 创建预览环境 KV...
npx wrangler kv:namespace create "test" --preview

echo.
echo ⚠️  请手动复制上面输出的 KV ID 到 wrangler.toml 文件中
echo    替换 id = "test" 和 preview_id = "test"
echo.
pause

REM 设置管理员密钥
echo 🔐 设置管理员密钥...
echo 请输入管理员密钥（至少32个字符）：
echo 建议使用: test_admin_key_2025_secure_minesweeper_cf_worker
echo.

npx wrangler secret put ADMIN_KEY
if errorlevel 1 (
    echo ❌ 管理员密钥设置失败
    pause
    exit /b 1
)

echo ✅ 管理员密钥设置成功
echo.

REM 部署Worker
echo 🚀 部署 Worker...
npx wrangler deploy
if errorlevel 1 (
    echo ❌ 部署失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 🎉 部署成功！
echo.
echo 📋 部署信息：
echo    Worker名称: test
echo    访问地址: https://test.你的用户名.workers.dev
echo.
echo 🧪 测试链接：
echo    游戏页面: https://test.你的用户名.workers.dev
echo    健康检查: https://test.你的用户名.workers.dev/health
echo    排行榜API: https://test.你的用户名.workers.dev/api/leaderboard/beginner
echo.
echo 🔧 管理API测试：
echo    1. 生成管理员令牌（参考 ADMIN_TOKEN_GENERATOR.md）
echo    2. 使用令牌访问: https://test.你的用户名.workers.dev/api/admin/stats
echo.
echo 📚 更多信息请查看：
echo    - QUICK_DEPLOY.md - 快速部署指南
echo    - DEPLOYMENT_GUIDE.md - 详细部署说明
echo    - FINAL_PROJECT_SUMMARY.md - 项目完整总结
echo.
echo ✨ 享受你的高性能、类型安全的扫雷游戏吧！
echo.
pause
