#!/bin/bash

# 🚀 CF-Minesweeper 测试部署脚本
# 作者：Claude 4.0 sonnet

echo "🚀 开始部署 cf-minesweeper 到 Cloudflare Workers..."
echo "Worker名称: test"
echo "KV命名空间: test"
echo ""

# 检查是否已登录
echo "📋 检查 Wrangler 登录状态..."
if ! npx wrangler whoami > /dev/null 2>&1; then
    echo "❌ 未登录 Cloudflare，请先登录："
    echo "   npx wrangler login"
    exit 1
fi

echo "✅ 已登录 Cloudflare"
echo ""

# 创建KV命名空间
echo "🗄️ 创建KV命名空间..."
echo "正在创建 KV 命名空间 'test'..."

# 创建生产环境KV
KV_OUTPUT=$(npx wrangler kv:namespace create "test" 2>&1)
echo "$KV_OUTPUT"

# 提取KV ID（如果创建成功）
if echo "$KV_OUTPUT" | grep -q "id ="; then
    KV_ID=$(echo "$KV_OUTPUT" | grep "id =" | sed 's/.*id = "\([^"]*\)".*/\1/')
    echo "✅ 生产环境 KV ID: $KV_ID"
else
    echo "⚠️  KV命名空间可能已存在，继续部署..."
fi

# 创建预览环境KV
echo "正在创建预览环境 KV 命名空间..."
PREVIEW_KV_OUTPUT=$(npx wrangler kv:namespace create "test" --preview 2>&1)
echo "$PREVIEW_KV_OUTPUT"

if echo "$PREVIEW_KV_OUTPUT" | grep -q "preview_id ="; then
    PREVIEW_KV_ID=$(echo "$PREVIEW_KV_OUTPUT" | grep "preview_id =" | sed 's/.*preview_id = "\([^"]*\)".*/\1/')
    echo "✅ 预览环境 KV ID: $PREVIEW_KV_ID"
fi

echo ""

# 更新wrangler.toml（如果获取到了新的ID）
if [ ! -z "$KV_ID" ] && [ ! -z "$PREVIEW_KV_ID" ]; then
    echo "📝 更新 wrangler.toml 配置..."
    
    # 备份原文件
    cp wrangler.toml wrangler.toml.backup
    
    # 更新KV ID
    sed -i.tmp "s/id = \"test\"/id = \"$KV_ID\"/" wrangler.toml
    sed -i.tmp "s/preview_id = \"test\"/preview_id = \"$PREVIEW_KV_ID\"/" wrangler.toml
    
    # 清理临时文件
    rm -f wrangler.toml.tmp
    
    echo "✅ wrangler.toml 已更新"
    echo "   生产环境 KV ID: $KV_ID"
    echo "   预览环境 KV ID: $PREVIEW_KV_ID"
else
    echo "⚠️  使用现有的 wrangler.toml 配置"
fi

echo ""

# 设置管理员密钥
echo "🔐 设置管理员密钥..."
echo "请输入管理员密钥（至少32个字符）："
echo "建议使用: test_admin_key_2025_secure_minesweeper_cf_worker"

if npx wrangler secret put ADMIN_KEY; then
    echo "✅ 管理员密钥设置成功"
else
    echo "❌ 管理员密钥设置失败"
    exit 1
fi

echo ""

# 部署Worker
echo "🚀 部署 Worker..."
if npx wrangler deploy; then
    echo ""
    echo "🎉 部署成功！"
    echo ""
    echo "📋 部署信息："
    echo "   Worker名称: test"
    echo "   访问地址: https://test.你的用户名.workers.dev"
    echo ""
    echo "🧪 测试链接："
    echo "   游戏页面: https://test.你的用户名.workers.dev"
    echo "   健康检查: https://test.你的用户名.workers.dev/health"
    echo "   排行榜API: https://test.你的用户名.workers.dev/api/leaderboard/beginner"
    echo ""
    echo "🔧 管理API测试："
    echo "   1. 生成管理员令牌（参考 ADMIN_TOKEN_GENERATOR.md）"
    echo "   2. 使用令牌访问: https://test.你的用户名.workers.dev/api/admin/stats"
    echo ""
    echo "📚 更多信息请查看："
    echo "   - QUICK_DEPLOY.md - 快速部署指南"
    echo "   - DEPLOYMENT_GUIDE.md - 详细部署说明"
    echo "   - FINAL_PROJECT_SUMMARY.md - 项目完整总结"
    echo ""
    echo "✨ 享受你的高性能、类型安全的扫雷游戏吧！"
else
    echo "❌ 部署失败，请检查错误信息"
    exit 1
fi
