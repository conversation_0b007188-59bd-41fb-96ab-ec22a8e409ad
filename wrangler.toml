name = "test"
main = "src/optimized.js"
compatibility_date = "2025-06-20"

# 🔒 简化方案：单KV命名空间 + 数据前缀分离
# 所有数据存储在一个KV中，通过键前缀区分类型
[[kv_namespaces]]
binding = "LEADERBOARD"
id = "cdcbd6d2a4dd4edcae593edbb9a5cd9d"
preview_id = "f5289ba9ff8d42b0845c6d05675085f7"

# 测试环境配置
[env.test]
name = "test"
[[env.test.kv_namespaces]]
binding = "LEADERBOARD"
id = "cdcbd6d2a4dd4edcae593edbb9a5cd9d"
preview_id = "f5289ba9ff8d42b0845c6d05675085f7"

[env.production]
name = "cf-minesweeper"
