# 🔒 管理员令牌生成工具

## 安全改进说明

为了修复管理API的安全漏洞，我们已经将认证方式从URL参数改为Authorization头部，并添加了时间戳验证防止重放攻击。

## 令牌格式

管理员令牌格式：`Bearer <base64(timestamp:signature)>`

其中：
- `timestamp`: 当前时间戳（毫秒）
- `signature`: 基于时间戳和管理密钥生成的签名

## 生成令牌的JavaScript代码

```javascript
// 管理员令牌生成函数
function generateAdminToken(adminKey) {
  const timestamp = Date.now().toString();
  
  // 生成签名（与服务端算法一致）
  const data = `${timestamp}:${adminKey}`;
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  const signature = Math.abs(hash).toString(36);
  
  // 组合并编码
  const tokenData = `${timestamp}:${signature}`;
  const token = btoa(tokenData);
  
  return token;
}

// 使用示例
const adminKey = "your-32-character-admin-key-here";
const token = generateAdminToken(adminKey);
console.log(`Authorization: Bearer ${token}`);
```

## 使用方法

1. 设置环境变量 `ADMIN_KEY`（至少32个字符）
2. 使用上述代码生成令牌
3. 在请求头中添加：`Authorization: Bearer <token>`

## 安全特性

- ✅ 令牌有效期：5分钟
- ✅ 防重放攻击：每个令牌只能使用一次
- ✅ 时间戳验证：防止过期令牌使用
- ✅ 安全存储：令牌不会出现在URL或日志中

## 示例请求

```bash
# 生成令牌
TOKEN=$(node -e "
const adminKey = 'your-admin-key-here';
const timestamp = Date.now().toString();
const data = timestamp + ':' + adminKey;
let hash = 0;
for (let i = 0; i < data.length; i++) {
  const char = data.charCodeAt(i);
  hash = ((hash << 5) - hash) + char;
  hash = hash & hash;
}
const signature = Math.abs(hash).toString(36);
const token = Buffer.from(timestamp + ':' + signature).toString('base64');
console.log(token);
")

# 使用令牌访问管理API
curl -H "Authorization: Bearer $TOKEN" \
     https://your-worker.workers.dev/api/admin/stats
```

## 注意事项

⚠️ **重要**：
- 管理密钥必须保密，不要提交到代码仓库
- 令牌有效期很短，需要及时使用
- 每个令牌只能使用一次
- 建议在生产环境中使用更强的加密算法（如HMAC-SHA256）
