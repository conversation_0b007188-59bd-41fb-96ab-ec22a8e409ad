# 🧪 测试部署指南

## 🚀 快速部署到测试环境

作为 **Claude 4.0 sonnet** 优化后的项目，现在可以快速部署到你的测试环境！

### 📋 部署配置
- **Worker名称**: `test`
- **KV命名空间**: `test`
- **环境**: 测试环境

## 🛠️ 部署步骤

### 方法1：使用自动化脚本（推荐）

#### Linux/macOS:
```bash
# 给脚本执行权限
chmod +x deploy-test.sh

# 运行部署脚本
./deploy-test.sh
```

#### Windows:
```cmd
# 直接运行批处理文件
deploy-test.bat
```

### 方法2：手动部署

#### 1. 登录Cloudflare
```bash
npx wrangler login
```

#### 2. 创建KV命名空间
```bash
# 创建生产环境KV
npx wrangler kv:namespace create "test"

# 创建预览环境KV  
npx wrangler kv:namespace create "test" --preview
```

#### 3. 更新wrangler.toml
将上述命令返回的ID替换到wrangler.toml中：
```toml
[[kv_namespaces]]
binding = "LEADERBOARD"
id = "你的KV_ID"
preview_id = "你的PREVIEW_KV_ID"
```

#### 4. 设置管理员密钥
```bash
npx wrangler secret put ADMIN_KEY
# 输入: test_admin_key_2025_secure_minesweeper_cf_worker
```

#### 5. 部署Worker
```bash
npx wrangler deploy
```

## 🧪 测试验证

### 1. 基础功能测试

#### 健康检查
```bash
curl https://test.你的用户名.workers.dev/health
```
期望输出：
```json
{
  "status": "healthy",
  "timestamp": "2025-06-21T...",
  "version": "2.0.0-security-enhanced"
}
```

#### 游戏页面
在浏览器中访问：`https://test.你的用户名.workers.dev`

#### 排行榜API
```bash
curl https://test.你的用户名.workers.dev/api/leaderboard/beginner
```

### 2. 性能测试

#### 缓存测试
```bash
# 第一次请求（缓存未命中）
time curl https://test.你的用户名.workers.dev/api/leaderboard/beginner

# 第二次请求（缓存命中，应该更快）
time curl https://test.你的用户名.workers.dev/api/leaderboard/beginner
```

#### 并发测试
```bash
# 使用ab工具测试并发性能
ab -n 100 -c 10 https://test.你的用户名.workers.dev/api/leaderboard/beginner
```

### 3. 安全测试

#### 管理API测试
首先生成管理员令牌：

```javascript
// 在浏览器控制台运行
function generateAdminToken(adminKey) {
  const timestamp = Date.now().toString();
  const data = `${timestamp}:${adminKey}`;
  let hash = 0;
  for (let i = 0; i < data.length; i++) {
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  const signature = Math.abs(hash).toString(36);
  const tokenData = `${timestamp}:${signature}`;
  const token = btoa(tokenData);
  return token;
}

// 使用你设置的管理员密钥
const token = generateAdminToken('test_admin_key_2025_secure_minesweeper_cf_worker');
console.log(`Authorization: Bearer ${token}`);
```

然后测试管理API：
```bash
# 使用生成的令牌
curl -H "Authorization: Bearer YOUR_TOKEN" \
     https://test.你的用户名.workers.dev/api/admin/stats
```

### 4. 功能测试

#### 成绩提交测试
```bash
curl -X POST https://test.你的用户名.workers.dev/api/leaderboard/beginner \
  -H "Content-Type: application/json" \
  -d '{
    "username": "测试用户",
    "time": 10,
    "gameData": {
      "difficulty": "beginner",
      "time": 10,
      "moves": 15,
      "gameId": "test_game_123",
      "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'",
      "boardSize": {"width": 9, "height": 9},
      "mineCount": 10,
      "gameEndTime": '$(date +%s000)',
      "firstClickTime": '$(($(date +%s000) - 10000))',
      "gameState": "won"
    }
  }'
```

## 📊 性能基准

### 预期性能指标
- **健康检查响应时间**: < 50ms
- **排行榜API（缓存命中）**: < 20ms
- **排行榜API（缓存未命中）**: < 150ms
- **成绩提交处理**: < 200ms
- **并发处理能力**: 15+ req/sec

### 缓存效果验证
- **首次访问**: 正常响应时间
- **重复访问**: 响应时间减少80-90%
- **缓存命中率**: 应达到70%以上

## 🐛 故障排除

### 常见问题

#### 1. KV命名空间错误
```
Error: KV namespace not found
```
**解决**: 检查wrangler.toml中的KV ID是否正确

#### 2. 管理API认证失败
```
{"success":false,"error":{"code":"UNAUTHORIZED"}}
```
**解决**: 
- 检查ADMIN_KEY是否设置
- 验证令牌生成是否正确
- 确保使用Authorization头部而非URL参数

#### 3. 部署失败
```
Error: Failed to publish
```
**解决**:
- 检查网络连接
- 确认已登录Cloudflare
- 验证wrangler.toml配置

### 调试技巧

#### 查看Worker日志
```bash
npx wrangler tail
```

#### 本地开发模式
```bash
npx wrangler dev
```

#### 检查KV数据
```bash
# 列出所有键
npx wrangler kv:key list --binding=LEADERBOARD

# 查看特定键的值
npx wrangler kv:key get "leaderboard:beginner" --binding=LEADERBOARD
```

## ✅ 测试清单

- [ ] Worker部署成功
- [ ] KV命名空间创建成功
- [ ] 管理员密钥设置成功
- [ ] 健康检查通过
- [ ] 游戏页面正常加载
- [ ] 排行榜API正常响应
- [ ] 缓存功能正常工作
- [ ] 成绩提交功能正常
- [ ] 管理API认证正常
- [ ] 性能指标达到预期

## 🎉 测试完成

测试完成后，你将拥有一个：
- 🔒 **企业级安全**的扫雷游戏
- ⚡ **高性能缓存**的API服务
- 🧹 **类型安全**的代码基础
- 📊 **完整监控**的运行环境

享受你的高质量扫雷游戏吧！🎮
